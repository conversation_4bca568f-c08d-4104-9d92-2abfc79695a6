## Hướng dẫn deploy Node.js project lên VPS sử dụng aaPanel và Nginx

### 1. Cài đặt Node.js trên VPS (qua aaPanel)
- Vào aaPanel > App Store > tìm và cài đặt Node.js.
- Hoặc SSH vào VPS và cài Node.js thủ công:
  ```bash
  curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
  apt-get install -y nodejs
  ```

### 2. Upload source code lên VPS
- Dùng FTP/SFTP hoặc File Manager của aaPanel để upload toàn bộ thư mục `chatbot_admin_backend` lên VPS (ví dụ: `/www/wwwroot/chatbot_admin_backend`).

### 3. Cài đặt các package cần thiết
```bash
cd /www/wwwroot/chatbot_admin_backend
npm install
```

### 4. Thi<PERSON>t lập biến môi trường (nế<PERSON> c<PERSON>)
- Tạo file `.env` (nếu dự án dùng dotenv) và cấu hình các biến như `DATABASE_URL`, v.v.

### 5. Chạy thử project
```bash
node app.js
# hoặc nếu dùng file khác
node index.js
```
- Nếu muốn chạy nền, dùng pm2:
```bash
npm install -g pm2
pm2 start app.js --name chatbot_admin_backend
pm2 save
pm2 startup
```

---

## Dùng Nginx làm reverse proxy (Khuyến nghị)

1. **Cài đặt Nginx** (nếu chưa có):
    - Qua aaPanel App Store hoặc:
      ```bash
      apt install nginx
      ```

2. **Cấu hình Nginx reverse proxy**
    - Vào aaPanel > Website > Add Site > chọn Reverse Proxy hoặc chỉnh file cấu hình Nginx:
    ```
    server {
        listen 80;
        server_name your-domain.com;

        location / {
            proxy_pass http://127.0.0.1:3000; # Port Node.js app
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    ```
    - Thay `your-domain.com` và port Node.js app cho đúng.

3. **Reload Nginx**
    ```bash
    systemctl reload nginx
    ```

---

## Tóm tắt
- Node.js sẽ chạy app (port 3000 hoặc port bạn chọn).
- Nginx sẽ nhận request ngoài, chuyển tiếp vào Node.js (reverse proxy).
- Quản lý tiến trình Node.js bằng pm2 để tự động restart khi lỗi/server reboot.

**Nên dùng Nginx reverse proxy để bảo mật và dễ cấu hình SSL.**
# GitHub Copilot Instructions for chatbot_admin_backend

## Project Overview
This project is a Node.js backend using Express, Prisma ORM, and related middleware. It is structured with routes, a public directory for static files, and uses Prisma for database management.

## Coding Guidelines
- Use modern JavaScript (ES6+) syntax.
- Follow the existing code style and structure.
- Use async/await for asynchronous operations.
- Place new routes in the `routes/` directory.
- Use Prisma Client for all database interactions.
- Validate and sanitize all user input.
- Use environment variables for configuration and secrets.

## File/Folder Conventions
- `app.js`: Main Express app setup.
- `bin/www`: Entry point for starting the server.
- `routes/`: Route handlers (add new route files here as needed).
- `prisma/`: Prisma schema and migrations.
- `public/`: Static files (HTML, CSS, etc.).

## Best Practices
- Keep controllers and business logic separate from route definitions.
- Handle errors gracefully and return appropriate HTTP status codes.
- Document new endpoints and functions with concise comments.
- Write modular, reusable code.

## Copilot Usage
- Suggest code that fits the above conventions and best practices.
- When generating new files, follow the folder structure and naming conventions.
- When in doubt, prefer clarity and maintainability over brevity.
