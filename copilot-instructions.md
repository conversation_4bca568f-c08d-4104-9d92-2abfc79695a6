# GitHub Copilot Instructions for chatbot_admin_backend

## Project Overview
This project is a Node.js backend using Express, Prisma ORM, and related middleware. It is structured with routes, a public directory for static files, and uses Prisma for database management.

## Coding Guidelines
- Use modern JavaScript (ES6+) syntax.
- Follow the existing code style and structure.
- Use async/await for asynchronous operations.
- Place new routes in the `routes/` directory.
- Use Prisma Client for all database interactions.
- Validate and sanitize all user input.
- Use environment variables for configuration and secrets.

## File/Folder Conventions
- `app.js`: Main Express app setup.
- `bin/www`: Entry point for starting the server.
- `routes/`: Route handlers (add new route files here as needed).
- `prisma/`: Prisma schema and migrations.
- `public/`: Static files (HTML, CSS, etc.).

## Best Practices
- Keep controllers and business logic separate from route definitions.
- Handle errors gracefully and return appropriate HTTP status codes.
- Document new endpoints and functions with concise comments.
- Write modular, reusable code.

## Copilot Usage
- Suggest code that fits the above conventions and best practices.
- When generating new files, follow the folder structure and naming conventions.
- When in doubt, prefer clarity and maintainability over brevity.
