const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/products';

// Test data
const testProduct = {
  product_name: "Test Product",
  price: "15$",
  images: ["https://example.com/test-image.jpg"],
  ingredients: "Test ingredients",
  instructions: "Test instructions",
  benefits: "Test benefits",
  usage: "Test usage",
  notes: "Test notes",
  otherInfo: "Test other info",
  targetUser: "Test target user",
  purpose: "Test purpose",
  description: "Test description",
  faqs: [
    {
      question: "Test question?",
      answer: "Test answer"
    }
  ]
};

async function testAPI() {
  console.log('🧪 Starting API Tests...\n');

  try {
    // Test 1: Create a product
    console.log('1️⃣ Testing CREATE product...');
    const createResponse = await axios.post(BASE_URL, testProduct);
    console.log('✅ CREATE Success:', createResponse.data.message);
    const createdProductId = createResponse.data.data.id;
    console.log(`   Created product ID: ${createdProductId}\n`);

    // Test 2: Get all products
    console.log('2️⃣ Testing GET all products...');
    const getAllResponse = await axios.get(BASE_URL);
    console.log('✅ GET ALL Success:', `Found ${getAllResponse.data.data.length} products`);
    console.log(`   Total products: ${getAllResponse.data.pagination.total}\n`);

    // Test 3: Get single product
    console.log('3️⃣ Testing GET single product...');
    const getSingleResponse = await axios.get(`${BASE_URL}/${createdProductId}`);
    console.log('✅ GET SINGLE Success:', getSingleResponse.data.data.name);
    console.log(`   Product price: $${getSingleResponse.data.data.price}\n`);

    // Test 4: Update product (PATCH)
    console.log('4️⃣ Testing PATCH product...');
    const patchData = {
      price: 25,
      description: "Updated test description"
    };
    const patchResponse = await axios.patch(`${BASE_URL}/${createdProductId}`, patchData);
    console.log('✅ PATCH Success:', patchResponse.data.message);
    console.log(`   Updated price: $${patchResponse.data.data.price}\n`);

    // Test 5: Search products
    console.log('5️⃣ Testing SEARCH products...');
    const searchResponse = await axios.get(`${BASE_URL}?search=Test&limit=5`);
    console.log('✅ SEARCH Success:', `Found ${searchResponse.data.data.length} products matching "Test"`);
    console.log(`   First result: ${searchResponse.data.data[0]?.name || 'None'}\n`);

    // Test 6: Test soft delete (deactivate)
    console.log('6️⃣ Testing DEACTIVATE product...');
    const deactivateResponse = await axios.put(`${BASE_URL}/${createdProductId}/deactivate`);
    console.log('✅ DEACTIVATE Success:', deactivateResponse.data.message);
    console.log(`   Product still exists but marked as deactivated\n`);

    // Test 7: Check if product can be deleted
    console.log('7️⃣ Testing DELETE CHECK...');
    const deleteCheckResponse = await axios.get(`${BASE_URL}/${createdProductId}/delete-check`);
    console.log('✅ DELETE CHECK Success:', deleteCheckResponse.data.data.canDelete ? 'Can delete' : 'Cannot delete');
    console.log(`   Blocking reasons: ${deleteCheckResponse.data.data.blockingReasons.join(', ') || 'None'}`);
    console.log(`   Related data: ${deleteCheckResponse.data.data.relatedData.orderItems} order items, ${deleteCheckResponse.data.data.relatedData.faqs} FAQs\n`);

    // Test 8: Delete product
    console.log('8️⃣ Testing DELETE product...');
    try {
      const deleteResponse = await axios.delete(`${BASE_URL}/${createdProductId}`);
      console.log('✅ DELETE Success:', deleteResponse.data.message);

      // Test 9: Verify deletion
      console.log('9️⃣ Testing GET deleted product (should fail)...');
      try {
        await axios.get(`${BASE_URL}/${createdProductId}`);
        console.log('❌ DELETE verification failed - product still exists');
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log('✅ DELETE verified - product not found (as expected)\n');
        } else {
          throw error;
        }
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('⚠️ DELETE blocked due to foreign key constraints (expected behavior)');
        console.log('   Message:', error.response.data.message);
        console.log('   Suggestion:', error.response.data.suggestion || 'Use deactivate endpoint instead');
        console.log('   This is normal if the product is referenced in orders\n');
      } else {
        throw error;
      }
    }

    // Test 10: Bulk import
    console.log('🔟 Testing BULK IMPORT...');
    const bulkData = {
      products: [
        { ...testProduct, product_name: "Bulk Test Product 1" },
        { ...testProduct, product_name: "Bulk Test Product 2", price: "20$" }
      ]
    };
    const bulkResponse = await axios.post(`${BASE_URL}/bulk-import`, bulkData);
    console.log('✅ BULK IMPORT Success:', bulkResponse.data.message);
    console.log(`   Created ${bulkResponse.data.data.created.length} products\n`);

    console.log('🎉 All API tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Check if server is running first
async function checkServer() {
  try {
    await axios.get('http://localhost:3000');
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 Checking if server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on http://localhost:3000');
    console.log('   Please start the server with: npm start');
    return;
  }
  
  console.log('✅ Server is running\n');
  await testAPI();
}

// Run the tests
main();
