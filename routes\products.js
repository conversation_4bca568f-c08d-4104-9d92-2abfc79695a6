const express = require('express');
const { PrismaClient } = require('@prisma/client');
const router = express.Router();

const prisma = new PrismaClient();

// Helper function to parse price from string format like "20$" to float
const parsePrice = (priceString) => {
  if (typeof priceString === 'number') return priceString;
  if (typeof priceString === 'string') {
    const numericValue = priceString.replace(/[^0-9.]/g, '');
    return parseFloat(numericValue) || 0;
  }
  return 0;
};

// GET /api/products - Get all products with optional pagination and search
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const where = search ? {
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { ingredients: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const orderBy = { [sortBy]: sortOrder };

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy,
        include: {
          faqs: true
        }
      }),
      prisma.product.count({ where })
    ]);

    res.json({
      success: true,
      data: products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/products/:id - Get a single product by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      include: {
        faqs: true
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// POST /api/products - Create a new product
router.post('/', async (req, res) => {
  try {
    const {
      product_name,
      price,
      images = [],
      ingredients,
      instructions,
      benefits,
      usage,
      notes,
      otherInfo,
      targetUser,
      purpose,
      description,
      faqs = []
    } = req.body;

    // Validation
    if (!product_name || !price || !ingredients || !instructions || !benefits || !usage) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: product_name, price, ingredients, instructions, benefits, usage'
      });
    }

    const parsedPrice = parsePrice(price);
    
    const product = await prisma.product.create({
      data: {
        name: product_name,
        price: parsedPrice,
        imageUrls: Array.isArray(images) ? images : [],
        ingredients,
        instructions,
        benefits,
        usage,
        notes,
        otherInfo,
        targetUser,
        purpose,
        description,
        faqs: {
          create: faqs.map(faq => ({
            question: faq.question,
            answer: faq.answer
          }))
        }
      },
      include: {
        faqs: true
      }
    });

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: product
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// PUT /api/products/:id - Update a product
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      product_name,
      price,
      images,
      ingredients,
      instructions,
      benefits,
      usage,
      notes,
      otherInfo,
      targetUser,
      purpose,
      description,
      faqs
    } = req.body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Prepare update data
    const updateData = {};
    if (product_name !== undefined) updateData.name = product_name;
    if (price !== undefined) updateData.price = parsePrice(price);
    if (images !== undefined) updateData.imageUrls = Array.isArray(images) ? images : [];
    if (ingredients !== undefined) updateData.ingredients = ingredients;
    if (instructions !== undefined) updateData.instructions = instructions;
    if (benefits !== undefined) updateData.benefits = benefits;
    if (usage !== undefined) updateData.usage = usage;
    if (notes !== undefined) updateData.notes = notes;
    if (otherInfo !== undefined) updateData.otherInfo = otherInfo;
    if (targetUser !== undefined) updateData.targetUser = targetUser;
    if (purpose !== undefined) updateData.purpose = purpose;
    if (description !== undefined) updateData.description = description;

    // Handle FAQs update if provided
    if (faqs !== undefined) {
      // Delete existing FAQs and create new ones
      await prisma.productFAQ.deleteMany({
        where: { productId: parseInt(id) }
      });
      
      updateData.faqs = {
        create: faqs.map(faq => ({
          question: faq.question,
          answer: faq.answer
        }))
      };
    }

    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        faqs: true
      }
    });

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// DELETE /api/products/:id - Delete a product
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      include: {
        orderItems: true
      }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product is referenced in any orders
    if (existingProduct.orderItems && existingProduct.orderItems.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete product because it is referenced in existing orders',
        details: `Product is used in ${existingProduct.orderItems.length} order item(s)`
      });
    }

    // Use transaction to ensure both FAQs and product are deleted together
    await prisma.$transaction(async (tx) => {
      // Delete FAQs first
      await tx.productFAQ.deleteMany({
        where: { productId: parseInt(id) }
      });

      // Then delete the product
      await tx.product.delete({
        where: { id: parseInt(id) }
      });
    });

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2003') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete product because it is referenced by other records',
        error: 'Foreign key constraint violation'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// POST /api/products/bulk-import - Import products from JSON data
router.post('/bulk-import', async (req, res) => {
  try {
    const { products } = req.body;

    if (!Array.isArray(products) || products.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Products array is required and must not be empty'
      });
    }

    const createdProducts = [];
    const errors = [];

    for (let i = 0; i < products.length; i++) {
      try {
        const productData = products[i];
        const {
          product_name,
          price,
          images = [],
          ingredients,
          instructions,
          benefits,
          usage,
          notes,
          otherInfo,
          targetUser,
          purpose,
          description,
          faqs = []
        } = productData;

        // Validation for each product
        if (!product_name || !price || !ingredients || !instructions || !benefits || !usage) {
          errors.push({
            index: i,
            message: 'Missing required fields: product_name, price, ingredients, instructions, benefits, usage'
          });
          continue;
        }

        const parsedPrice = parsePrice(price);

        const product = await prisma.product.create({
          data: {
            name: product_name,
            price: parsedPrice,
            imageUrls: Array.isArray(images) ? images : [],
            ingredients,
            instructions,
            benefits,
            usage,
            notes,
            otherInfo,
            targetUser,
            purpose,
            description,
            faqs: {
              create: faqs.map(faq => ({
                question: faq.question,
                answer: faq.answer
              }))
            }
          },
          include: {
            faqs: true
          }
        });

        createdProducts.push(product);
      } catch (error) {
        errors.push({
          index: i,
          message: error.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: `Bulk import completed. ${createdProducts.length} products created successfully.`,
      data: {
        created: createdProducts,
        errors: errors
      }
    });
  } catch (error) {
    console.error('Error in bulk import:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// PUT /api/products/:id/deactivate - Soft delete (deactivate) a product
router.put('/:id/deactivate', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Add isActive field to the product (this would require a schema update)
    // For now, we'll use a workaround by adding a note to indicate deactivation
    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(id) },
      data: {
        notes: existingProduct.notes ?
          `${existingProduct.notes}\n\n[DEACTIVATED: ${new Date().toISOString()}]` :
          `[DEACTIVATED: ${new Date().toISOString()}]`
      },
      include: {
        faqs: true
      }
    });

    res.json({
      success: true,
      message: 'Product deactivated successfully',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Error deactivating product:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// PATCH /api/products/:id - Partially update a product
router.patch('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateFields = req.body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Prepare update data (only include provided fields)
    const updateData = {};

    if (updateFields.product_name !== undefined) updateData.name = updateFields.product_name;
    if (updateFields.price !== undefined) updateData.price = parsePrice(updateFields.price);
    if (updateFields.images !== undefined) updateData.imageUrls = Array.isArray(updateFields.images) ? updateFields.images : [];
    if (updateFields.ingredients !== undefined) updateData.ingredients = updateFields.ingredients;
    if (updateFields.instructions !== undefined) updateData.instructions = updateFields.instructions;
    if (updateFields.benefits !== undefined) updateData.benefits = updateFields.benefits;
    if (updateFields.usage !== undefined) updateData.usage = updateFields.usage;
    if (updateFields.notes !== undefined) updateData.notes = updateFields.notes;
    if (updateFields.otherInfo !== undefined) updateData.otherInfo = updateFields.otherInfo;
    if (updateFields.targetUser !== undefined) updateData.targetUser = updateFields.targetUser;
    if (updateFields.purpose !== undefined) updateData.purpose = updateFields.purpose;
    if (updateFields.description !== undefined) updateData.description = updateFields.description;

    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        faqs: true
      }
    });

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;
