# Products CRUD API

This document describes the Products CRUD API that was created based on the `data/products_only.json` file.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
# or
yarn install
```

### 2. Setup Database
Make sure your PostgreSQL database is running and the `DATABASE_URL` is configured in your `.env` file.

```bash
# Run Prisma migrations
npx prisma migrate dev
```

### 3. Import Sample Data
```bash
npm run import-products
```

### 4. Start the Server
```bash
npm start
```

The server will start on `http://localhost:3000`

### 5. Test the API
```bash
npm run test-api
```

## 📋 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products` | Get all products (with pagination, search, sorting) |
| GET | `/api/products/:id` | Get a single product by ID |
| POST | `/api/products` | Create a new product |
| PUT | `/api/products/:id` | Update a product (full update) |
| PATCH | `/api/products/:id` | Partially update a product |
| DELETE | `/api/products/:id` | Delete a product |
| POST | `/api/products/bulk-import` | Import multiple products |

## 📊 Data Structure

The API handles products with the following structure:

```json
{
  "id": 1,
  "name": "Giò heo hun khói",
  "price": 20,
  "imageUrls": ["https://example.com/image1.jpg"],
  "ingredients": "Giò heo nguyên chiếc, Muối hạt, Tiêu sọ...",
  "instructions": "Ngăn mát tủ lạnh: 7–10 ngày...",
  "benefits": "Cung cấp protein cao...",
  "usage": "Rã đông tự nhiên 30 phút...",
  "notes": "Chân giò heo trước hoặc sau...",
  "otherInfo": "Không tái cấp đông sau khi rã đông...",
  "targetUser": "Phù hợp mọi lứa tuổi...",
  "purpose": "Làm món ăn vặt, ăn chơi...",
  "description": "Không tái cấp đông sau khi rã đông...",
  "createdAt": "2025-01-24T10:00:00.000Z",
  "updatedAt": "2025-01-24T10:00:00.000Z",
  "faqs": [
    {
      "id": 1,
      "productId": 1,
      "question": "Giò heo hun khói là gì?",
      "answer": "Là món ăn làm từ chân giò heo..."
    }
  ]
}
```

## 🔍 Features

### Pagination
```bash
GET /api/products?page=1&limit=10
```

### Search
```bash
GET /api/products?search=heo
```

### Sorting
```bash
GET /api/products?sortBy=name&sortOrder=asc
```

### Combined Query
```bash
GET /api/products?page=1&limit=5&search=heo&sortBy=price&sortOrder=desc
```

## 📝 Example Usage

### Create a Product
```bash
curl -X POST http://localhost:3000/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "New Product",
    "price": "25$",
    "ingredients": "Test ingredients",
    "instructions": "Test instructions",
    "benefits": "Test benefits",
    "usage": "Test usage",
    "faqs": [
      {
        "question": "What is this product?",
        "answer": "This is a test product."
      }
    ]
  }'
```

### Update a Product
```bash
curl -X PATCH http://localhost:3000/api/products/1 \
  -H "Content-Type: application/json" \
  -d '{
    "price": 30,
    "description": "Updated description"
  }'
```

### Delete a Product
```bash
curl -X DELETE http://localhost:3000/api/products/1
```

## 🛠️ Scripts

- `npm run import-products` - Import sample data from JSON file
- `npm run test-api` - Run API tests
- `npm start` - Start the server

## 📁 File Structure

```
├── routes/
│   └── products.js          # Products API routes
├── scripts/
│   ├── import-products.js   # Data import script
│   └── test-api.js         # API testing script
├── docs/
│   └── products-api.md     # Detailed API documentation
├── data/
│   └── products_only.json # Sample product data
└── prisma/
    └── schema.prisma       # Database schema
```

## 🔧 Database Schema

The API uses the following Prisma models:

- **Product**: Main product entity
- **ProductFAQ**: FAQ entries linked to products

See `prisma/schema.prisma` for the complete schema definition.

## 🚨 Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## 📖 Full Documentation

For detailed API documentation with request/response examples, see `docs/products-api.md`.

## 🧪 Testing

The API includes comprehensive tests that cover:
- Creating products
- Retrieving products (single and multiple)
- Updating products (full and partial)
- Deleting products
- Bulk importing
- Search and pagination

Run tests with: `npm run test-api`

## 🔄 Data Migration

The original JSON data structure has been mapped to the database schema as follows:

| JSON Field | Database Field | Notes |
|------------|----------------|-------|
| `product_name` | `name` | Product name |
| `price` | `price` | Parsed from string format (e.g., "20$" → 20) |
| `images` | `imageUrls` | Array of image URLs |
| `faqs` | `faqs` | Related ProductFAQ records |
| All other fields | Same name | Direct mapping |

## 🤝 Contributing

When adding new features:
1. Update the Prisma schema if needed
2. Add corresponding API endpoints
3. Update documentation
4. Add tests
5. Update the import script if data structure changes
