const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const prisma = new PrismaClient();

// Helper function to parse price from string format like "20$" to float
const parsePrice = (priceString) => {
  if (typeof priceString === 'number') return priceString;
  if (typeof priceString === 'string') {
    const numericValue = priceString.replace(/[^0-9.]/g, '');
    return parseFloat(numericValue) || 0;
  }
  return 0;
};

async function importProducts() {
  try {
    // Read the JSON file
    const jsonPath = path.join(__dirname, '..', 'data', 'products_only.json');
    const jsonData = fs.readFileSync(jsonPath, 'utf8');
    const products = JSON.parse(jsonData);

    console.log(`Found ${products.length} products to import...`);

    for (let i = 0; i < products.length; i++) {
      const productData = products[i];

      const imagesString = productData.images;
      const images = imagesString ? imagesString.split(', ') : [];
      
      try {
        const {
          product_name,
          price,
          ingredients,
          instructions,
          benefits,
          usage,
          notes,
          otherInfo,
          targetUser,
          purpose,
          description,
          faqs = []
        } = productData;

        // Check if product already exists
        const existingProduct = await prisma.product.findFirst({
          where: { name: product_name }
        });

        if (existingProduct) {
          console.log(`Product "${product_name}" already exists, skipping...`);
          continue;
        }

        const parsedPrice = parsePrice(price);
        
        const product = await prisma.product.create({
          data: {
            name: product_name,
            price: parsedPrice,
            imageUrls: Array.isArray(images) ? images : [],
            ingredients,
            instructions,
            benefits,
            usage,
            notes,
            otherInfo,
            targetUser,
            purpose,
            description,
            faqs: {
              create: faqs.map(faq => ({
                question: faq.question,
                answer: faq.answer
              }))
            }
          },
          include: {
            faqs: true
          }
        });

        console.log(`✅ Created product: ${product.name} (ID: ${product.id})`);
      } catch (error) {
        console.error(`❌ Error creating product ${i + 1}:`, error.message);
      }
    }

    console.log('✅ Import completed successfully!');
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
importProducts();
